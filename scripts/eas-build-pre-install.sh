#!/bin/bash

# EAS Build Pre-Install Hook
# This script runs before dependencies are installed during EAS build

echo "🔧 Running EAS Build Pre-Install Hook..."

# Create google-services.json from environment variable if it exists
if [ -n "$GOOGLE_SERVICES_JSON" ]; then
  echo "📱 Creating google-services.json from environment variable..."
  echo "$GOOGLE_SERVICES_JSON" > google-services.json
  echo "✅ google-services.json created successfully"
else
  echo "⚠️  GOOGLE_SERVICES_JSON environment variable not found"
  echo "   This is required for Android builds with Google Services"
fi

echo "✅ EAS Build Pre-Install Hook completed"
