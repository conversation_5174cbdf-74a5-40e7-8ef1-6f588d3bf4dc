{"cli": {"version": ">= 16.17.3", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "env": {"GOOGLE_SERVICES_JSON": "$GOOGLE_SERVICES_JSON"}, "prebuildCommand": "bash scripts/eas-build-pre-install.sh"}, "preview": {"distribution": "internal", "env": {"GOOGLE_SERVICES_JSON": "$GOOGLE_SERVICES_JSON"}, "prebuildCommand": "bash scripts/eas-build-pre-install.sh"}, "production": {"autoIncrement": true, "env": {"GOOGLE_SERVICES_JSON": "$GOOGLE_SERVICES_JSON"}, "prebuildCommand": "bash scripts/eas-build-pre-install.sh"}}, "submit": {"production": {}}}