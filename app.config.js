import { config } from 'dotenv';

// Load environment variables from appropriate file
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.local';
config({ path: envFile });

export default {
  expo: {
    name: "App-PlantConnects-290725",
    slug: "app-plantsconnect-290725",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "plantconnects",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    splash: {
      image: "./assets/images/splash-icon.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.plantconnects.app",
      infoPlist: {
        NSCameraUsageDescription: "Allow $(PRODUCT_NAME) to access your camera",
        NSMicrophoneUsageDescription: "Allow $(PRODUCT_NAME) to access your microphone",
        NSPhotoLibraryUsageDescription: "Allow $(PRODUCT_NAME) to access your photos"
      },
      // associatedDomains: ["applinks:plantconnects.com"] // Removed - plantconnects.com is external website
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      package: "com.plantconnects.app",
      permissions: [
        "android.permission.VIBRATE",
        "CAMERA",
        "RECORD_AUDIO",
        "READ_EXTERNAL_STORAGE",
        "WRITE_EXTERNAL_STORAGE"
      ],
      intentFilters: [
        {
          action: "VIEW",
          autoVerify: true,
          // Removed plantconnects.com - it's an external website
          // data: [
          //   {
          //     scheme: "https",
          //     host: "plantconnects.com"
          //   }
          // ],
          category: ["BROWSABLE", "DEFAULT"]
        }
      ],
      googleServicesFile: process.env.GOOGLE_SERVICES_JSON ? "./google-services.json" : undefined,
      config: {
        googleSignIn: {
          androidClientId: process.env.GOOGLE_ANDROID_CLIENT_ID || "YOUR_NEW_ANDROID_CLIENT_ID_HERE"
        }
      }
    },
    web: {
      favicon: "./assets/images/favicon.png"
    },
    plugins: [
      [
        "expo-router",
        {
          // origin: "https://plantconnects.com/" // Removed - external website
        }
      ],
      [
        "expo-camera",
        {
          cameraPermission: "Allow $(PRODUCT_NAME) to access your camera",
          microphonePermission: "Allow $(PRODUCT_NAME) to access your microphone",
          recordAudioAndroid: false
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "The app accesses your photos to let you share them with your friends."
        }
      ],
      "expo-web-browser"
    ],
    experiments: {
      typedRoutes: true
    },
    extra: {
      supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL || "https://zlivouxymzpbyoxnwxrp.supabase.co",
      supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_KEY || "sb_publishable_ABgFmonyDTQFCL9IiD9Pzw_H7-dr7yC",
      openrouterApiKey: process.env.OPENROUTER_API_KEY || "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",
      openrouterModel: process.env.OPENROUTER_MODEL || "google/gemini-2.5-flash-lite",
      // Cloudflare R2 Configuration
      cloudflareAccountId: process.env.CLOUDFLARE_ACCOUNT_ID,
      r2AccessKeyId: process.env.R2_ACCESS_KEY_ID,
      r2SecretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
      r2BucketName: process.env.R2_BUCKET_NAME,
      r2EndpointUrl: process.env.R2_ENDPOINT_URL,
      eas: {
        projectId: "d9be860a-b072-4b32-ba4f-7e1a492938f3"
      }
    }
  }
};
